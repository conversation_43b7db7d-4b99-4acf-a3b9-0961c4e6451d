(function(global, factory){
  if(typeof module === 'object' && module.exports) {
    module.exports = factory();
  } else {
    global.Tasks = factory();
  }
}(this, function(){
  function createTask(description, tags = [], deadline = null, priority = null) {
    return {
      id: Date.now() + Math.random(),
      description,
      tags,
      deadline,
      priority
    };
  }

  function addTask(list, task) {
    list.push(task);
    return list;
  }

  function uniqueTags(list) {
    const set = new Set();
    list.forEach(t => {
      t.tags.forEach(tag => set.add(tag));
    });
    return Array.from(set);
  }

  function filterTasksByTag(list, tag) {
    return list.filter(t => t.tags.includes(tag));
  }

  function getInboxTasks(list) {
    const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format

    return list.filter(task => {
      // 1. Task non categorizzati (senza tags) - solo se non hanno priorità speciali con date future
      const isUncategorized = task.tags.length === 0 && (
        !task.priority ||
        task.priority === 'ASAP' ||
        task.priority === 'Anytime' ||
        (task.priority === 'Evento' && task.deadline === today) ||
        (task.priority === 'Deadline' && task.deadline && task.deadline <= today)
      );

      // 2. Eventi di oggi (anche se hanno tags)
      const isTodayEvent = task.priority === 'Evento' && task.deadline === today;

      // 3. Deadline oggi o scaduti (anche se hanno tags)
      const isDeadlineToday = task.priority === 'Deadline' && task.deadline && task.deadline <= today;

      // 4. Task ASAP (anche se hanno tags) - sempre urgenti
      const isASAP = task.priority === 'ASAP';

      return isUncategorized || isTodayEvent || isDeadlineToday || isASAP;
    });
  }

  function updateTask(list, updatedTask) {
    return list.map(task => task.id === updatedTask.id ? updatedTask : task);
  }

  function deleteTask(list, taskId) {
    return list.filter(task => task.id !== taskId);
  }

  return { createTask, addTask, uniqueTags, filterTasksByTag, getInboxTasks, updateTask, deleteTask };
}));

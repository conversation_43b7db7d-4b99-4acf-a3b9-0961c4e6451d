(function(global, factory){
  if(typeof module === 'object' && module.exports) {
    module.exports = factory();
  } else {
    global.Tasks = factory();
  }
}(this, function(){
  function createTask(description, tags = [], deadline = null) {
    return {
      id: Date.now() + Math.random(),
      description,
      tags,
      deadline
    };
  }

  function addTask(list, task) {
    list.push(task);
    return list;
  }

  function uniqueTags(list) {
    const set = new Set();
    list.forEach(t => {
      t.tags.forEach(tag => set.add(tag));
    });
    return Array.from(set);
  }

  function filterTasksByTag(list, tag) {
    return list.filter(t => t.tags.includes(tag));
  }

  return { createTask, addTask, uniqueTags, filterTasksByTag };
}));

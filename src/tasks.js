(function(global, factory){
  if(typeof module === 'object' && module.exports) {
    module.exports = factory();
  } else {
    global.Tasks = factory();
  }
}(this, function(){
  function createTask(description, tags = [], deadline = null, priority = null, folder = null) {
    return {
      id: Date.now() + Math.random(),
      description,
      tags,
      deadline,
      priority,
      folder
    };
  }

  function addTask(list, task) {
    list.push(task);
    return list;
  }

  function uniqueTags(list) {
    const set = new Set();
    list.forEach(t => {
      t.tags.forEach(tag => set.add(tag));
    });
    return Array.from(set);
  }

  function filterTasksByTag(list, tag) {
    return list.filter(t => t.tags.includes(tag));
  }

  function getInboxTasks(list) {
    const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format

    return list.filter(task => {
      // 1. Task non categorizzati (senza tags) - solo se non hanno priorità Anytime
      const isUncategorized = task.tags.length === 0 && (
        !task.priority ||
        task.priority === 'ASAP' ||
        (task.priority === 'Evento' && task.deadline === today) ||
        task.priority === 'Deadline'
      );

      // 2. Eventi di oggi (anche se hanno tags)
      const isTodayEvent = task.priority === 'Evento' && task.deadline === today;

      // 3. Deadline SEMPRE (anche se hanno tags) - finché non completata
      const isDeadline = task.priority === 'Deadline';

      // 4. Task ASAP (anche se hanno tags) - sempre urgenti
      const isASAP = task.priority === 'ASAP';

      return isUncategorized || isTodayEvent || isDeadline || isASAP;
    });
  }

  function updateTask(list, updatedTask) {
    return list.map(task => task.id === updatedTask.id ? updatedTask : task);
  }

  function deleteTask(list, taskId) {
    return list.filter(task => task.id !== taskId);
  }

  // Folder system functions
  function uniqueFolders(list) {
    const folders = new Set();
    list.forEach(task => {
      if (task.folder) {
        folders.add(task.folder);
      }
    });
    return Array.from(folders).sort();
  }

  function buildFolderTree(folders) {
    const tree = {};

    folders.forEach(folder => {
      const parts = folder.split('/');
      let current = tree;

      parts.forEach((part, index) => {
        if (!current[part]) {
          current[part] = {
            name: part,
            fullPath: parts.slice(0, index + 1).join('/'),
            children: {},
            isLeaf: index === parts.length - 1
          };
        }
        current = current[part].children;
      });
    });

    return tree;
  }

  function filterTasksByFolder(list, folderPath) {
    return list.filter(task =>
      task.folder && (task.folder === folderPath || task.folder.startsWith(folderPath + '/'))
    );
  }

  function isFolder(tag) {
    return tag.includes('/');
  }

  function getParentFolder(folderPath) {
    const lastSlash = folderPath.lastIndexOf('/');
    return lastSlash > 0 ? folderPath.substring(0, lastSlash) : null;
  }

  return {
    createTask,
    addTask,
    uniqueTags,
    uniqueFolders,
    filterTasksByTag,
    getInboxTasks,
    updateTask,
    deleteTask,
    buildFolderTree,
    filterTasksByFolder,
    isFolder,
    getParentFolder
  };
}));

const {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Container,
  <PERSON>,
  CssB<PERSON><PERSON>,
  <PERSON>er,
  IconButton,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  Divider,
  Button,
  TextField,
  Card,
  CardContent,
  Chip,
  Stack,
  Paper,
  ThemeProvider,
  createTheme,
  Fab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Autocomplete,
  FormControl,
  FormLabel,
  ToggleButtonGroup,
  ToggleButton,
  Menu,
  MenuItem
} = MaterialUI;

// Tema Material-UI
const theme = createTheme({
  palette: {
    primary: {
      main: '#d32f2f', // Rosso per "Red Tasks"
    },
    secondary: {
      main: '#f44336',
    },
  },
});

// Icone Material-UI
const MenuIcon = () => <span className="material-icons">menu</span>;
const AddIcon = () => <span className="material-icons">add</span>;
const InboxIcon = () => <span className="material-icons">inbox</span>;
const LabelIcon = () => <span className="material-icons">label</span>;
const EditIcon = () => <span className="material-icons">edit</span>;
const DeleteIcon = () => <span className="material-icons">delete</span>;
const FolderIcon = () => <span className="material-icons">folder</span>;
const FolderOpenIcon = () => <span className="material-icons">folder_open</span>;
const ExpandMoreIcon = () => <span className="material-icons">expand_more</span>;
const ChevronRightIcon = () => <span className="material-icons">chevron_right</span>;

function App() {
  const [tasks, setTasks] = React.useState([]);
  const [filterTag, setFilterTag] = React.useState(null);
  const [filterFolder, setFilterFolder] = React.useState(null);
  const [description, setDescription] = React.useState('');
  const [selectedTags, setSelectedTags] = React.useState([]);
  const [deadline, setDeadline] = React.useState('');
  const [drawerOpen, setDrawerOpen] = React.useState(false);
  const [dialogOpen, setDialogOpen] = React.useState(false);
  const [priorityType, setPriorityType] = React.useState('');
  const [editingTask, setEditingTask] = React.useState(null);
  const [contextMenu, setContextMenu] = React.useState(null);
  const [scrollProgress, setScrollProgress] = React.useState(0);
  const [expandedFolders, setExpandedFolders] = React.useState(new Set());

  const tags = Tasks.uniqueTags(tasks);
  const folderTree = Tasks.buildFolderTree(tags);

  const displayed = React.useMemo(() => {
    if (filterTag) {
      return Tasks.filterTasksByTag(tasks, filterTag);
    } else if (filterFolder) {
      return Tasks.filterTasksByFolder(tasks, filterFolder);
    } else {
      return Tasks.getInboxTasks(tasks);
    }
  }, [tasks, filterTag, filterFolder]);

  const priorityTypes = ['Evento', 'Deadline', 'ASAP', 'Anytime'];
  const needsDate = priorityType === 'Evento' || priorityType === 'Deadline';
  const isEditing = editingTask !== null;

  function openEditDialog(task) {
    setEditingTask(task);
    setDescription(task.description);
    setSelectedTags(task.tags);
    setDeadline(task.deadline || '');
    setPriorityType(task.priority || '');
    setDialogOpen(true);
    setContextMenu(null);
  }

  function openCreateDialog() {
    setEditingTask(null);
    setDescription('');
    setSelectedTags([]);
    setDeadline('');
    setPriorityType('');
    setDialogOpen(true);
  }

  function handleContextMenu(event, task) {
    event.preventDefault();
    setContextMenu({
      mouseX: event.clientX - 2,
      mouseY: event.clientY - 4,
      task: task
    });
  }

  function closeContextMenu() {
    setContextMenu(null);
  }

  function closeDialog() {
    setDialogOpen(false);
    setEditingTask(null);
    setDescription('');
    setSelectedTags([]);
    setDeadline('');
    setPriorityType('');
  }

  function handleScroll(event) {
    const { scrollTop, scrollHeight, clientHeight } = event.target;
    const progress = scrollHeight > clientHeight ? (scrollTop / (scrollHeight - clientHeight)) * 100 : 0;
    setScrollProgress(progress);
  }

  function toggleFolder(folderPath) {
    setExpandedFolders(prev => {
      const newSet = new Set(prev);
      if (newSet.has(folderPath)) {
        newSet.delete(folderPath);
      } else {
        newSet.add(folderPath);
      }
      return newSet;
    });
  }

  function selectFolder(folderPath) {
    setFilterFolder(folderPath);
    setFilterTag(null);
  }

  function selectTag(tag) {
    setFilterTag(tag);
    setFilterFolder(null);
  }

  function clearFilters() {
    setFilterTag(null);
    setFilterFolder(null);
  }

  function deleteTask(taskId) {
    setTasks(prev => prev.filter(t => t.id !== taskId));
    setContextMenu(null);
  }

  function onSubmit(ev) {
    ev.preventDefault();
    if (!description.trim()) return;

    if (isEditing) {
      // Update existing task
      const updatedTask = {
        ...editingTask,
        description: description.trim(),
        tags: selectedTags,
        deadline: needsDate ? deadline : null,
        priority: priorityType
      };
      setTasks(prev => prev.map(t => t.id === editingTask.id ? updatedTask : t));
    } else {
      // Create new task
      const task = Tasks.createTask(description.trim(), selectedTags, needsDate ? deadline : null, priorityType);
      setTasks(prev => [...prev, task]);
    }

    // Reset form
    setDescription('');
    setSelectedTags([]);
    setDeadline('');
    setPriorityType('');
    setEditingTask(null);
    setDialogOpen(false);
  }

  function renderFolderTree(tree, level = 0) {
    return Object.values(tree).map(folder => {
      const isExpanded = expandedFolders.has(folder.fullPath);
      const hasChildren = Object.keys(folder.children).length > 0;
      const isSelected = filterFolder === folder.fullPath || filterTag === folder.fullPath;

      return (
        <React.Fragment key={folder.fullPath}>
          <ListItemButton
            selected={isSelected}
            onClick={() => {
              if (hasChildren) {
                toggleFolder(folder.fullPath);
              }
              if (folder.isLeaf || !hasChildren) {
                if (Tasks.isFolder(folder.fullPath)) {
                  selectFolder(folder.fullPath);
                } else {
                  selectTag(folder.fullPath);
                }
              }
            }}
            sx={{
              pl: 2 + level * 2,
              minHeight: 40,
              '&:hover': {
                backgroundColor: 'rgba(0, 0, 0, 0.04)',
              }
            }}
          >
            {hasChildren && (
              <IconButton
                size="small"
                onClick={(e) => {
                  e.stopPropagation();
                  toggleFolder(folder.fullPath);
                }}
                sx={{ mr: 0.5, p: 0.5 }}
              >
                {isExpanded ? <ExpandMoreIcon /> : <ChevronRightIcon />}
              </IconButton>
            )}

            {hasChildren ? (
              isExpanded ? <FolderOpenIcon /> : <FolderIcon />
            ) : (
              <LabelIcon />
            )}

            <ListItemText
              primary={folder.name}
              sx={{
                ml: hasChildren ? 1 : 2,
                '& .MuiListItemText-primary': {
                  fontSize: level > 0 ? '0.875rem' : '1rem',
                  fontWeight: isSelected ? 600 : 400,
                }
              }}
            />
          </ListItemButton>

          {hasChildren && isExpanded && (
            <Box>
              {renderFolderTree(folder.children, level + 1)}
            </Box>
          )}
        </React.Fragment>
      );
    });
  }

  const drawerWidth = 240;

  return (
    <Box sx={{ display: 'flex' }}>
      <CssBaseline />

      {/* App Bar */}
      <AppBar position="fixed" sx={{ zIndex: (theme) => theme.zIndex.drawer + 1 }}>
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={() => setDrawerOpen(!drawerOpen)}
            sx={{ mr: 2, display: { sm: 'none' } }}
          >
            <MenuIcon />
          </IconButton>
          <Typography variant="h6" noWrap component="div">
            Red Tasks
          </Typography>
        </Toolbar>
      </AppBar>

      {/* Sidebar Drawer */}
      <Drawer
        variant="permanent"
        sx={{
          width: drawerWidth,
          flexShrink: 0,
          display: { xs: 'none', sm: 'block' },
          '& .MuiDrawer-paper': {
            width: drawerWidth,
            boxSizing: 'border-box',
          },
        }}
      >
        <Toolbar />
        <Box sx={{ overflow: 'auto', p: 2 }}>
          <Typography variant="h6" gutterBottom>
            Filtri
          </Typography>

          <List>
            <ListItemButton
              selected={filterTag === null && filterFolder === null}
              onClick={clearFilters}
            >
              <InboxIcon />
              <ListItemText primary="Inbox" sx={{ ml: 1 }} />
            </ListItemButton>

            <Divider sx={{ my: 1 }} />

            {Object.keys(folderTree).length > 0 && (
              <>
                <Typography variant="subtitle2" sx={{ px: 2, py: 1, color: 'text.secondary' }}>
                  Cartelle e Tags
                </Typography>
                {renderFolderTree(folderTree)}
              </>
            )}
          </List>
        </Box>
      </Drawer>

      {/* Main Content */}
      <Box component="main" sx={{ flexGrow: 1, p: 3 }}>
        <Toolbar />

        <Container maxWidth="md">
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h4">
              {filterTag ? `Tag: ${filterTag}` :
               filterFolder ? `Cartella: ${filterFolder}` :
               'Inbox'}
            </Typography>
            <Chip
              label={`${displayed.length} task${displayed.length !== 1 ? 's' : ''}`}
              color="primary"
              variant="outlined"
              size="small"
            />
          </Box>



          {/* Tasks List */}
          <Box sx={{ position: 'relative' }}>
            <Stack
              spacing={2}
              onScroll={handleScroll}
              sx={{
                scrollSnapType: 'y mandatory',
                overflowY: 'auto',
                maxHeight: 'calc(100vh - 200px)',
                '&::-webkit-scrollbar': {
                  width: '8px',
                },
                '&::-webkit-scrollbar-track': {
                  background: 'rgba(0,0,0,0.1)',
                  borderRadius: '4px',
                },
                '&::-webkit-scrollbar-thumb': {
                  background: 'rgba(0,0,0,0.3)',
                  borderRadius: '4px',
                  '&:hover': {
                    background: 'rgba(0,0,0,0.5)',
                  },
                },
              }}
            >
            {displayed.length === 0 ? (
              <Typography variant="body1" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
                {filterTag ? `Nessuna task con tag "${filterTag}"` :
                 filterFolder ? `Nessuna task nella cartella "${filterFolder}"` :
                 'Nessuna task presente'}
              </Typography>
            ) : (
              displayed.map(task => (
                <Card
                  key={task.id}
                  variant="outlined"
                  onContextMenu={(e) => handleContextMenu(e, task)}
                  sx={{
                    cursor: 'context-menu',
                    scrollSnapAlign: 'start',
                    transition: 'all 0.2s ease-in-out',
                    position: 'relative',
                    borderLeft: task.priority === 'ASAP' ? '4px solid #f44336' :
                               task.priority === 'Deadline' ? '4px solid #ff9800' :
                               task.priority === 'Evento' ? '4px solid #2196f3' : 'none',
                    '&:hover': {
                      transform: 'translateY(-2px)',
                      boxShadow: 3,
                      borderLeftWidth: task.priority ? '6px' : 'none',
                    },
                    animation: 'fadeInUp 0.3s ease-out',
                    '@keyframes fadeInUp': {
                      '0%': {
                        opacity: 0,
                        transform: 'translateY(20px)',
                      },
                      '100%': {
                        opacity: 1,
                        transform: 'translateY(0)',
                      },
                    },
                  }}
                >
                  <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                      <Typography variant="h6" gutterBottom sx={{ mb: 0 }}>
                        {task.description}
                      </Typography>
                      {task.priority && (
                        <Chip
                          label={task.priority}
                          size="small"
                          color={task.priority === 'ASAP' ? 'error' : task.priority === 'Deadline' ? 'warning' : 'primary'}
                          variant="filled"
                        />
                      )}
                    </Box>

                    {task.deadline && (
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Scadenza: {new Date(task.deadline).toLocaleDateString('it-IT')}
                      </Typography>
                    )}

                    {task.tags.length > 0 && (
                      <Stack direction="row" spacing={1} sx={{ mt: 1 }}>
                        {task.tags.map(tag => (
                          <Chip
                            key={tag}
                            label={tag}
                            size="small"
                            variant="outlined"
                            onClick={() => setFilterTag(tag)}
                            sx={{ cursor: 'pointer' }}
                          />
                        ))}
                      </Stack>
                    )}
                  </CardContent>
                </Card>
              ))
            )}
            </Stack>

            {/* Scroll Progress Indicator */}
            {displayed.length > 3 && (
              <Box
                sx={{
                  position: 'absolute',
                  right: 0,
                  top: 0,
                  bottom: 0,
                  width: '3px',
                  background: 'rgba(0,0,0,0.1)',
                  borderRadius: '2px',
                }}
              >
                <Box
                  sx={{
                    width: '100%',
                    height: `${scrollProgress}%`,
                    background: 'linear-gradient(to bottom, #d32f2f, #f44336)',
                    borderRadius: '2px',
                    transition: 'height 0.1s ease-out',
                  }}
                />
              </Box>
            )}
          </Box>
        </Container>
      </Box>

      {/* Floating Action Button */}
      <Fab
        color="primary"
        aria-label="add"
        sx={{
          position: 'fixed',
          bottom: 16,
          right: 16,
        }}
        onClick={openCreateDialog}
      >
        <AddIcon />
      </Fab>

      {/* Add/Edit Task Dialog */}
      <Dialog
        open={dialogOpen}
        onClose={closeDialog}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>{isEditing ? 'Modifica task' : 'Aggiungi nuova task'}</DialogTitle>
        <DialogContent>
          <Box component="form" onSubmit={onSubmit} sx={{ display: 'flex', flexDirection: 'column', gap: 2, pt: 1 }}>
            <TextField
              label="Descrizione"
              variant="outlined"
              fullWidth
              value={description}
              onChange={(ev) => setDescription(ev.target.value)}
              required
              autoFocus
            />

            <Autocomplete
              multiple
              options={tags}
              value={selectedTags}
              onChange={(event, newValue) => setSelectedTags(newValue)}
              freeSolo
              renderTags={(value, getTagProps) =>
                value.map((option, index) => (
                  <Chip variant="outlined" label={option} {...getTagProps({ index })} />
                ))
              }
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Tags e Cartelle"
                  placeholder="es: lavoro, Casa/Cucina, Progetti/Web"
                  helperText="Usa '/' per creare cartelle: Casa/Cucina crea la cartella Casa con sottocartella Cucina"
                />
              )}
            />

            <FormControl>
              <FormLabel component="legend">Priorità</FormLabel>
              <Stack direction="row" spacing={1} sx={{ mt: 1, flexWrap: 'wrap', gap: 1 }}>
                {priorityTypes.map((type) => (
                  <Chip
                    key={type}
                    label={type}
                    clickable
                    color={priorityType === type ? 'primary' : 'default'}
                    variant={priorityType === type ? 'filled' : 'outlined'}
                    onClick={() => setPriorityType(priorityType === type ? '' : type)}
                  />
                ))}
              </Stack>
            </FormControl>

            {needsDate && (
              <TextField
                label="Data"
                type="date"
                variant="outlined"
                value={deadline}
                onChange={(ev) => setDeadline(ev.target.value)}
                InputLabelProps={{
                  shrink: true,
                }}
                required
              />
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={closeDialog}>Annulla</Button>
          <Button
            onClick={onSubmit}
            variant="contained"
            disabled={!description.trim()}
          >
            {isEditing ? 'Salva Modifiche' : 'Aggiungi Task'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Context Menu */}
      <Menu
        open={contextMenu !== null}
        onClose={closeContextMenu}
        anchorReference="anchorPosition"
        anchorPosition={
          contextMenu !== null
            ? { top: contextMenu.mouseY, left: contextMenu.mouseX }
            : undefined
        }
      >
        <MenuItem onClick={() => openEditDialog(contextMenu?.task)}>
          <EditIcon sx={{ mr: 1 }} />
          Modifica
        </MenuItem>
        <MenuItem onClick={() => deleteTask(contextMenu?.task?.id)}>
          <DeleteIcon sx={{ mr: 1 }} />
          Elimina
        </MenuItem>
      </Menu>
    </Box>
  );
}

// Render dell'app
const domContainer = document.getElementById('root');
const root = ReactDOM.createRoot(domContainer);
root.render(
  <ThemeProvider theme={theme}>
    <App />
  </ThemeProvider>
);

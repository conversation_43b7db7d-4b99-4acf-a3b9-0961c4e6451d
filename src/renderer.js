const {
  A<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Typography,
  Container,
  Box,
  CssBase<PERSON>,
  Drawer,
  IconButton,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  Divider,
  Button,
  TextField,
  Card,
  CardContent,
  Chip,
  Stack,
  Paper,
  ThemeProvider,
  createTheme,
  Fab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Autocomplete,
  FormControl,
  FormLabel,
  ToggleButtonGroup,
  ToggleButton
} = MaterialUI;

// Tema Material-UI
const theme = createTheme({
  palette: {
    primary: {
      main: '#d32f2f', // Rosso per "Red Tasks"
    },
    secondary: {
      main: '#f44336',
    },
  },
});

// Icone Material-UI
const MenuIcon = () => <span className="material-icons">menu</span>;
const AddIcon = () => <span className="material-icons">add</span>;
const InboxIcon = () => <span className="material-icons">inbox</span>;
const LabelIcon = () => <span className="material-icons">label</span>;

function App() {
  const [tasks, setTasks] = React.useState([]);
  const [filterTag, setFilterTag] = React.useState(null);
  const [description, setDescription] = React.useState('');
  const [selectedTags, setSelectedTags] = React.useState([]);
  const [deadline, setDeadline] = React.useState('');
  const [drawerOpen, setDrawerOpen] = React.useState(false);
  const [dialogOpen, setDialogOpen] = React.useState(false);
  const [priorityType, setPriorityType] = React.useState('');

  const tags = Tasks.uniqueTags(tasks);
  const displayed = filterTag ? Tasks.filterTasksByTag(tasks, filterTag) : tasks;

  const priorityTypes = ['Evento', 'Deadline', 'ASAP', 'Anytime'];
  const needsDate = priorityType === 'Evento' || priorityType === 'Deadline';

  function onSubmit(ev) {
    ev.preventDefault();
    if (!description.trim()) return;

    // Only use normal tags, not priority types
    const task = Tasks.createTask(description.trim(), selectedTags, needsDate ? deadline : null, priorityType);
    setTasks(prev => [...prev, task]);

    // Reset form
    setDescription('');
    setSelectedTags([]);
    setDeadline('');
    setPriorityType('');
    setDialogOpen(false);
  }

  const drawerWidth = 240;

  return (
    <Box sx={{ display: 'flex' }}>
      <CssBaseline />

      {/* App Bar */}
      <AppBar position="fixed" sx={{ zIndex: (theme) => theme.zIndex.drawer + 1 }}>
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={() => setDrawerOpen(!drawerOpen)}
            sx={{ mr: 2, display: { sm: 'none' } }}
          >
            <MenuIcon />
          </IconButton>
          <Typography variant="h6" noWrap component="div">
            Red Tasks
          </Typography>
        </Toolbar>
      </AppBar>

      {/* Sidebar Drawer */}
      <Drawer
        variant="permanent"
        sx={{
          width: drawerWidth,
          flexShrink: 0,
          display: { xs: 'none', sm: 'block' },
          '& .MuiDrawer-paper': {
            width: drawerWidth,
            boxSizing: 'border-box',
          },
        }}
      >
        <Toolbar />
        <Box sx={{ overflow: 'auto', p: 2 }}>
          <Typography variant="h6" gutterBottom>
            Filtri
          </Typography>

          <List>
            <ListItemButton
              selected={filterTag === null}
              onClick={() => setFilterTag(null)}
            >
              <InboxIcon />
              <ListItemText primary="Inbox" sx={{ ml: 1 }} />
            </ListItemButton>

            <Divider sx={{ my: 1 }} />

            {tags.map(tag => (
              <ListItemButton
                key={tag}
                selected={filterTag === tag}
                onClick={() => setFilterTag(tag)}
              >
                <LabelIcon />
                <ListItemText primary={tag} sx={{ ml: 1 }} />
              </ListItemButton>
            ))}
          </List>
        </Box>
      </Drawer>

      {/* Main Content */}
      <Box component="main" sx={{ flexGrow: 1, p: 3 }}>
        <Toolbar />

        <Container maxWidth="md">
          <Typography variant="h4" gutterBottom>
            {filterTag ? `Tasks: ${filterTag}` : 'Tutte le Tasks'}
          </Typography>



          {/* Tasks List */}
          <Stack spacing={2}>
            {displayed.length === 0 ? (
              <Typography variant="body1" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
                {filterTag ? `Nessuna task con tag "${filterTag}"` : 'Nessuna task presente'}
              </Typography>
            ) : (
              displayed.map(task => (
                <Card key={task.id} variant="outlined">
                  <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                      <Typography variant="h6" gutterBottom sx={{ mb: 0 }}>
                        {task.description}
                      </Typography>
                      {task.priority && (
                        <Chip
                          label={task.priority}
                          size="small"
                          color={task.priority === 'ASAP' ? 'error' : task.priority === 'Deadline' ? 'warning' : 'primary'}
                          variant="filled"
                        />
                      )}
                    </Box>

                    {task.deadline && (
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Scadenza: {new Date(task.deadline).toLocaleDateString('it-IT')}
                      </Typography>
                    )}

                    {task.tags.length > 0 && (
                      <Stack direction="row" spacing={1} sx={{ mt: 1 }}>
                        {task.tags.map(tag => (
                          <Chip
                            key={tag}
                            label={tag}
                            size="small"
                            variant="outlined"
                            onClick={() => setFilterTag(tag)}
                            sx={{ cursor: 'pointer' }}
                          />
                        ))}
                      </Stack>
                    )}
                  </CardContent>
                </Card>
              ))
            )}
          </Stack>
        </Container>
      </Box>

      {/* Floating Action Button */}
      <Fab
        color="primary"
        aria-label="add"
        sx={{
          position: 'fixed',
          bottom: 16,
          right: 16,
        }}
        onClick={() => setDialogOpen(true)}
      >
        <AddIcon />
      </Fab>

      {/* Add Task Dialog */}
      <Dialog
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Aggiungi nuova task</DialogTitle>
        <DialogContent>
          <Box component="form" onSubmit={onSubmit} sx={{ display: 'flex', flexDirection: 'column', gap: 2, pt: 1 }}>
            <TextField
              label="Descrizione"
              variant="outlined"
              fullWidth
              value={description}
              onChange={(ev) => setDescription(ev.target.value)}
              required
              autoFocus
            />

            <Autocomplete
              multiple
              options={tags}
              value={selectedTags}
              onChange={(event, newValue) => setSelectedTags(newValue)}
              freeSolo
              renderTags={(value, getTagProps) =>
                value.map((option, index) => (
                  <Chip variant="outlined" label={option} {...getTagProps({ index })} />
                ))
              }
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Tags"
                  placeholder="Seleziona o aggiungi tags"
                  helperText="Puoi selezionare tags esistenti o crearne di nuovi"
                />
              )}
            />

            <FormControl>
              <FormLabel component="legend">Priorità</FormLabel>
              <Stack direction="row" spacing={1} sx={{ mt: 1, flexWrap: 'wrap', gap: 1 }}>
                {priorityTypes.map((type) => (
                  <Chip
                    key={type}
                    label={type}
                    clickable
                    color={priorityType === type ? 'primary' : 'default'}
                    variant={priorityType === type ? 'filled' : 'outlined'}
                    onClick={() => setPriorityType(priorityType === type ? '' : type)}
                  />
                ))}
              </Stack>
            </FormControl>

            {needsDate && (
              <TextField
                label="Data"
                type="date"
                variant="outlined"
                value={deadline}
                onChange={(ev) => setDeadline(ev.target.value)}
                InputLabelProps={{
                  shrink: true,
                }}
                required
              />
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>Annulla</Button>
          <Button
            onClick={onSubmit}
            variant="contained"
            disabled={!description.trim()}
          >
            Aggiungi Task
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

// Render dell'app
const domContainer = document.getElementById('root');
const root = ReactDOM.createRoot(domContainer);
root.render(
  <ThemeProvider theme={theme}>
    <App />
  </ThemeProvider>
);

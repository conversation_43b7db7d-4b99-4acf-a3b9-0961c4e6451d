const assert = require('assert');
const Tasks = require('../src/tasks.js');

const list = [];
const t1 = Tasks.createTask('Task 1', ['work', 'urgent']);
const t2 = Tasks.createTask('Task 2', ['home']);
Tasks.addTask(list, t1);
Tasks.addTask(list, t2);

const workTasks = Tasks.filterTasksByTag(list, 'work');
assert.strictEqual(workTasks.length, 1);
assert.strictEqual(workTasks[0].description, 'Task 1');

const tags = Tasks.uniqueTags(list).sort();
assert.deepStrictEqual(tags, ['home', 'urgent', 'work']);

console.log('Tasks module tests passed');

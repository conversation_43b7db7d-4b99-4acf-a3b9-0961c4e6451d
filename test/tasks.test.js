const assert = require('assert');
const Tasks = require('../src/tasks.js');

const list = [];
const t1 = Tasks.createTask('Task 1', ['work', 'urgent']);
const t2 = Tasks.createTask('Task 2', ['home']);
Tasks.addTask(list, t1);
Tasks.addTask(list, t2);

const workTasks = Tasks.filterTasksByTag(list, 'work');
assert.strictEqual(workTasks.length, 1);
assert.strictEqual(workTasks[0].description, 'Task 1');

const tags = Tasks.uniqueTags(list).sort();
assert.deepStrictEqual(tags, ['home', 'urgent', 'work']);

// Test priority functionality
const t3 = Tasks.createTask('Task 3', ['work'], '2024-12-31', 'ASAP');
assert.strictEqual(t3.priority, 'ASAP');
assert.strictEqual(t3.deadline, '2024-12-31');

const t4 = Tasks.createTask('Task 4', ['personal'], null, 'Anytime');
assert.strictEqual(t4.priority, 'Anytime');
assert.strictEqual(t4.deadline, null);

// Test that priorities don't appear in uniqueTags
Tasks.addTask(list, t3);
Tasks.addTask(list, t4);
const tagsWithPriority = Tasks.uniqueTags(list).sort();
assert.deepStrictEqual(tagsWithPriority, ['home', 'personal', 'urgent', 'work']);
assert(!tagsWithPriority.includes('ASAP'));
assert(!tagsWithPriority.includes('Anytime'));

// Test Inbox functionality
const today = new Date().toISOString().split('T')[0];
const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split('T')[0];
const tomorrow = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split('T')[0];

const inboxTestTasks = [
  Tasks.createTask('Uncategorized task', []), // Should appear (no tags)
  Tasks.createTask('Tagged task', ['work']), // Should NOT appear (has tags)
  Tasks.createTask('Today event', ['work'], today, 'Evento'), // Should appear (event today)
  Tasks.createTask('Tomorrow event', [], tomorrow, 'Evento'), // Should NOT appear (event tomorrow)
  Tasks.createTask('Today deadline', ['personal'], today, 'Deadline'), // Should appear (deadline ALWAYS)
  Tasks.createTask('Yesterday deadline', [], yesterday, 'Deadline'), // Should appear (deadline ALWAYS)
  Tasks.createTask('Tomorrow deadline', [], tomorrow, 'Deadline'), // Should appear (deadline ALWAYS)
  Tasks.createTask('ASAP task', ['urgent'], null, 'ASAP'), // Should appear (ASAP priority)
  Tasks.createTask('ASAP uncategorized', [], null, 'ASAP'), // Should appear (ASAP priority)
  Tasks.createTask('Anytime task', [], null, 'Anytime'), // Should NOT appear (Anytime priority)
  Tasks.createTask('Anytime with tags', ['work'], null, 'Anytime'), // Should NOT appear (Anytime priority)
];

const inboxTasks = Tasks.getInboxTasks(inboxTestTasks);
assert.strictEqual(inboxTasks.length, 7, 'Should have 7 tasks in inbox');

// Verify specific tasks are included
const inboxDescriptions = inboxTasks.map(t => t.description);
assert(inboxDescriptions.includes('Uncategorized task'));
assert(inboxDescriptions.includes('Today event')); // Event today (even with tags)
assert(inboxDescriptions.includes('Today deadline')); // Deadline ALWAYS
assert(inboxDescriptions.includes('Yesterday deadline')); // Deadline ALWAYS
assert(inboxDescriptions.includes('Tomorrow deadline')); // Deadline ALWAYS
assert(inboxDescriptions.includes('ASAP uncategorized')); // ASAP priority
assert(inboxDescriptions.includes('ASAP task')); // ASAP priority (even with tags)

// Verify specific tasks are NOT included
assert(!inboxDescriptions.includes('Tagged task')); // Has tags but no special priority
assert(!inboxDescriptions.includes('Tomorrow event')); // Event tomorrow (not today)
assert(!inboxDescriptions.includes('Anytime task')); // Anytime priority (never in inbox)
assert(!inboxDescriptions.includes('Anytime with tags')); // Anytime priority (never in inbox)

// Test folder system (separate from tags)
const folderTasks = [
  Tasks.createTask('Root task', ['work'], null, null, null),
  Tasks.createTask('House task', ['cleaning'], null, null, 'House/Kitchen'),
  Tasks.createTask('House bedroom task', ['organizing'], null, null, 'House/Bedroom'),
  Tasks.createTask('Project task', ['coding'], null, null, 'Projects/Web/Frontend'),
  Tasks.createTask('Another project', ['mobile'], null, null, 'Projects/Mobile'),
];

const folderList = Tasks.uniqueFolders(folderTasks);
const folderTree = Tasks.buildFolderTree(folderList);

// Test folder tree structure
assert(folderTree.House, 'Should have House folder');
assert(folderTree.House.children.Kitchen, 'Should have Kitchen subfolder');
assert(folderTree.House.children.Bedroom, 'Should have Bedroom subfolder');
assert(folderTree.Projects, 'Should have Projects folder');
assert(folderTree.Projects.children.Web, 'Should have Web subfolder');
assert(folderTree.Projects.children.Web.children.Frontend, 'Should have Frontend sub-subfolder');

// Test folder filtering
const houseTasks = Tasks.filterTasksByFolder(folderTasks, 'House');
assert.strictEqual(houseTasks.length, 2, 'Should have 2 house tasks');

const projectTasks = Tasks.filterTasksByFolder(folderTasks, 'Projects');
assert.strictEqual(projectTasks.length, 2, 'Should have 2 project tasks');

const webTasks = Tasks.filterTasksByFolder(folderTasks, 'Projects/Web');
assert.strictEqual(webTasks.length, 1, 'Should have 1 web task');

// Test folder utilities
assert(Tasks.isFolder('House/Kitchen'), 'Should recognize folder path');
assert(!Tasks.isFolder('work'), 'Should not recognize simple tag as folder');
assert.strictEqual(Tasks.getParentFolder('House/Kitchen'), 'House', 'Should get parent folder');
assert.strictEqual(Tasks.getParentFolder('House'), null, 'Should return null for root folder');

// Test that folders and tags are separate
const taskTags = Tasks.uniqueTags(folderTasks);
assert(taskTags.includes('work'), 'Should have work tag');
assert(taskTags.includes('cleaning'), 'Should have cleaning tag');
assert(!taskTags.includes('House/Kitchen'), 'Should not include folder in tags');
assert(!taskTags.includes('Projects/Web'), 'Should not include folder in tags');

console.log('Tasks module tests passed');

const assert = require('assert');
const Tasks = require('../src/tasks.js');

const list = [];
const t1 = Tasks.createTask('Task 1', ['work', 'urgent']);
const t2 = Tasks.createTask('Task 2', ['home']);
Tasks.addTask(list, t1);
Tasks.addTask(list, t2);

const workTasks = Tasks.filterTasksByTag(list, 'work');
assert.strictEqual(workTasks.length, 1);
assert.strictEqual(workTasks[0].description, 'Task 1');

const tags = Tasks.uniqueTags(list).sort();
assert.deepStrictEqual(tags, ['home', 'urgent', 'work']);

// Test priority functionality
const t3 = Tasks.createTask('Task 3', ['work'], '2024-12-31', 'ASAP');
assert.strictEqual(t3.priority, 'ASAP');
assert.strictEqual(t3.deadline, '2024-12-31');

const t4 = Tasks.createTask('Task 4', ['personal'], null, 'Anytime');
assert.strictEqual(t4.priority, 'Anytime');
assert.strictEqual(t4.deadline, null);

// Test that priorities don't appear in uniqueTags
Tasks.addTask(list, t3);
Tasks.addTask(list, t4);
const tagsWithPriority = Tasks.uniqueTags(list).sort();
assert.deepStrictEqual(tagsWithPriority, ['home', 'personal', 'urgent', 'work']);
assert(!tagsWithPriority.includes('ASAP'));
assert(!tagsWithPriority.includes('Anytime'));

// Test Inbox functionality
const today = new Date().toISOString().split('T')[0];
const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split('T')[0];
const tomorrow = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split('T')[0];

const inboxTestTasks = [
  Tasks.createTask('Uncategorized task', []), // Should appear (no tags)
  Tasks.createTask('Tagged task', ['work']), // Should NOT appear (has tags)
  Tasks.createTask('Today event', ['work'], today, 'Evento'), // Should appear (event today)
  Tasks.createTask('Tomorrow event', [], tomorrow, 'Evento'), // Should NOT appear (event tomorrow)
  Tasks.createTask('Today deadline', ['personal'], today, 'Deadline'), // Should appear (deadline today)
  Tasks.createTask('Yesterday deadline', [], yesterday, 'Deadline'), // Should appear (deadline overdue)
  Tasks.createTask('Tomorrow deadline', [], tomorrow, 'Deadline'), // Should NOT appear (deadline future)
  Tasks.createTask('ASAP task', ['urgent'], null, 'ASAP'), // Should NOT appear (has tags)
  Tasks.createTask('ASAP uncategorized', [], null, 'ASAP'), // Should appear (no tags)
];

const inboxTasks = Tasks.getInboxTasks(inboxTestTasks);
assert.strictEqual(inboxTasks.length, 5, 'Should have 5 tasks in inbox');

// Verify specific tasks are included
const inboxDescriptions = inboxTasks.map(t => t.description);
assert(inboxDescriptions.includes('Uncategorized task'));
assert(inboxDescriptions.includes('Today event')); // Event today (even with tags)
assert(inboxDescriptions.includes('Today deadline')); // Deadline today (even with tags)
assert(inboxDescriptions.includes('Yesterday deadline')); // Deadline overdue
assert(inboxDescriptions.includes('ASAP uncategorized')); // No tags

// Verify specific tasks are NOT included
assert(!inboxDescriptions.includes('Tagged task')); // Has tags but no special priority
assert(!inboxDescriptions.includes('Tomorrow event')); // Event tomorrow
assert(!inboxDescriptions.includes('Tomorrow deadline')); // Deadline future
assert(!inboxDescriptions.includes('ASAP task')); // ASAP with tags (not special for inbox)

console.log('Tasks module tests passed');
